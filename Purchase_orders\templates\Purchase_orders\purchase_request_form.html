{% extends 'Purchase_orders/base_purchase_orders.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}إنشاء طلب شراء - نظام الدولية{% endblock %}

{% block page_title %}إنشاء طلب شراء جديد{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Purchase_orders:dashboard' %}">طلبات الشراء</a></li>
<li class="breadcrumb-item"><a href="{% url 'Purchase_orders:purchase_request_list' %}">قائمة طلبات الشراء</a></li>
<li class="breadcrumb-item active">إنشاء طلب جديد</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-end mb-4">
    <a href="{% url 'Purchase_orders:purchase_request_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">بيانات طلب الشراء</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.request_number.id_for_label }}" class="form-label">رقم الطلب</label>
                        {{ form.request_number|add_class:"form-control"|attr:"readonly:readonly" }}
                        {% if form.request_number.errors %}
                        <div class="text-danger">
                            {% for error in form.request_number.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.vendor.id_for_label }}" class="form-label">المورد</label>
                        {{ form.vendor|add_class:"form-select" }}
                        {% if form.vendor.errors %}
                        <div class="text-danger">
                            {% for error in form.vendor.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                {{ form.notes|add_class:"form-control" }}
                {% if form.notes.errors %}
                <div class="text-danger">
                    {% for error in form.notes.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <div class="form-text">يمكنك إضافة أي ملاحظات أو تفاصيل إضافية متعلقة بالطلب</div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="reset" class="btn btn-secondary">
                    <i class="fas fa-undo me-1"></i> إعادة تعيين
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ الطلب
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
